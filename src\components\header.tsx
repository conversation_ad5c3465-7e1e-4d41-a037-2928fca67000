import React, { Suspense } from "react"

import { LanguageSwitcher } from "./language-switcher"
import ToggleThemeButton from "./toggle-theme-button"

export default function Header() {
  return (
    <header className="bg-background/90 dark:bg-sidebar/85 border-muted sticky top-0 z-30 mx-auto flex h-20 w-[95%] max-w-7xl items-center justify-end gap-2 rounded-b-4xl! border-b-4 px-5 shadow-[0_-7px_20px_#00000025] backdrop-blur-md md:w-[90%] dark:border-b-1 dark:shadow-[0_2px_15px_#00000040]">
      <Suspense fallback={<div></div>}>
        <ToggleThemeButton />
        <LanguageSwitcher />
      </Suspense>
    </header>
  )
}
