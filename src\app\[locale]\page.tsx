import Link from "next/link"
import { ListEnd, Search, SquareArrowOutUpLeft } from "lucide-react"

import { <PERSON> } from "@/types/data"
import { GetDictionary, Locale } from "@/types/i18n"
import { getDictionary } from "@/lib/i18n"
import { getReciters } from "@/lib/reciters"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardAction,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { CommandItem } from "@/components/ui/command"
import CommandSearch from "@/components/command-search"

interface HomePageProps {
  params: Promise<{ locale: Locale }>
}

export default async function HomePage({ params }: HomePageProps) {
  const { locale } = await params
  const readers = getReciters(locale)
  const dict = (await getDictionary(locale)).home

  return (
    <div className="w-full text-center">
      <Hero dict={dict.hero} readers={readers} locale={locale} />
      <ReadersList dict={dict.readers} locale={locale} readers={readers} />
    </div>
  )
}

function Hero({
  dict,
  readers,
  locale,
}: {
  dict: GetDictionary["home"]["hero"]
  readers: Reader[]
  locale: Locale
}) {
  const { title, description, buttons } = dict

  return (
    <div className="relative -mt-20 flex h-[500px] w-full justify-center overflow-clip">
      <div
        tabIndex={-1}
        className="from-background to-secondary absolute bottom-0 -z-10 aspect-square w-500 rounded-full bg-radial from-69% to-71% pb-14 dark:to-black/25"
      >
        <div className="from-primary-foreground/20 dark:from-muted/30 dark:to-muted/30 to-primary-foreground aspect-square w-full rounded-full bg-radial from-69% to-71%" />
      </div>

      <div className="mt-36">
        <h1 className="text-primary text-4xl sm:text-7xl dark:text-foreground">{title}</h1>
        <p className="text-muted-foreground mt-2 px-6 sm:text-xl">
          {description}
        </p>
        <div className="mt-12 flex justify-center gap-x-2">
          <Button asChild size="lg">
            <Link href={`/${locale}#readers`}>
              <ListEnd />
              {buttons.browse}
            </Link>
          </Button>
          <CommandSearch
            dialogTrigger={
              <Button variant="outline" size="lg">
                <Search />
                {buttons.search}
              </Button>
            }
            commandEmptyLabel=" No results found."
            commandInputPlaceholder="Search for a reader..."
          >
            {readers.map((reader) => (
              <CommandItem asChild key={reader.id} value={reader.name}>
                <Link prefetch={false} href={`/${locale}/${reader.id}`}>
                  <Search />
                  {reader.name}
                </Link>
              </CommandItem>
            ))}
          </CommandSearch>
        </div>
      </div>
    </div>
  )
}

function ReadersList({
  readers,
  dict,
  locale,
}: {
  readers: Reader[]
  dict: GetDictionary["home"]["readers"]
  locale: Locale
}) {
  return (
    <div
      id="readers"
      className="mx-auto mt-40 grid w-full max-w-6xl scroll-mt-28 grid-cols-1 gap-6 px-4 sm:grid-cols-2 lg:grid-cols-3"
    >
      {readers.map((reader) => (
        <Card
          key={reader.id}
          className="mx-auto w-full scroll-mt-28 items-center self-stretch overflow-clip text-center"
          id={reader.id}
        >
          <CardHeader className="w-full">
            <CardTitle className="text-muted-foreground line-clamp-2 text-xl font-semibold text-balance">
              {reader.name}
            </CardTitle>
          </CardHeader>
          <CardContent className="mt-auto">
            <Badge variant="outline">
              {dict.surahsCount} : {reader.surahsCount}
            </Badge>
          </CardContent>
          <CardFooter className="w-full">
            <CardAction className="w-full">
              <Link href={`/${locale}/${reader.id}`}>
                <Button className="w-full">
                  <SquareArrowOutUpLeft /> {dict.listen}
                </Button>
              </Link>
            </CardAction>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
