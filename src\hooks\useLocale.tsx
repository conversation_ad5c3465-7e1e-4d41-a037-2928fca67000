"use client"

// useLocale.tsx
import * as React from "react"

import { LocaleConfig } from "@/types/i18n"

const LocaleContext = React.createContext<LocaleConfig | null>(null!)

export function LocaleProvider({
  children,
  value,
}: {
  children: React.ReactNode
  value: LocaleConfig
}) {
  return <LocaleContext value={value}>{children}</LocaleContext>
}

export function useLocale() {
  const context = React.useContext(LocaleContext)
  if (!context) {
    throw new Error("useLocale must be used within a LocaleProvider")
  }
  return context
}
