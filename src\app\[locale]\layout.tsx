import type { <PERSON>ada<PERSON>, Viewport } from "next"

import "./globals.css"

import { Cairo } from "next/font/google"

import { Locale } from "@/types/i18n"
import { i18nConfig } from "@/config/i18n"
import { getDictionary, getLocaleConfig } from "@/lib/i18n"
import { AudioPlayingProvider } from "@/hooks/useAudioPlaying"
import { DictionaryProvider } from "@/hooks/useDictionary"
import { LocaleProvider } from "@/hooks/useLocale"
import AudioPlayer from "@/components/audio-player"
import Header from "@/components/header"
import { ThemeProvider } from "@/components/theme-provider"

const cairo = Cairo({
  subsets: ["arabic"],
})

interface Props {
  children: React.ReactNode
  params: Promise<{ locale: Locale }>
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: dark)", color: "#09090b" },
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
  ],
}

export const dynamicParams = false

export async function generateStaticParams() {
  return i18nConfig.locales.map((locale) => ({
    locale: locale.code,
  }))
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params
  const DOMAIN = process.env.DOMAIN!

  const dict = (await getDictionary(locale)).metadata.mainLayout

  return {
    generator: "Next.js + Tailwind CSS",
    metadataBase: new URL(DOMAIN),
    title: dict.title,
    description: dict.description,
    icons: "/icon.png",
    alternates: {
      canonical: "/",
      languages: {
        ar: "/ar",
        en: "/en",
      },
    },
    openGraph: {
      type: "website",
      title: dict.title,
      description: dict.description,
      url: DOMAIN,
      siteName: dict.title,
      images:
        locale === "ar"
          ? { url: "/og-image-ar.png", width: 1200, height: 630 }
          : { url: "/og-image-en.png", width: 1200, height: 630 },
      locale: "ar",
      alternateLocale: ["ar", "en"],
    },
    twitter: {
      card: "summary_large_image",
      title: dict.title,
      description: dict.description,
      images: locale === "ar" ? "/og-image-ar.png" : "/og-image-en.png",
    },
    robots: {
      index: true,
      follow: true,
      nocache: false,
      googleBot: {
        index: true,
        follow: true,
        noimageindex: false,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  }
}

export default async function LocaleLayout({ children, params }: Props) {
  const { locale } = await params
  const dict = await getDictionary(locale)

  const localeConfig = getLocaleConfig(locale)
  return (
    <html
      lang={locale}
      dir={localeConfig.dir}
      className={`${locale} scroll-smooth`}
      suppressHydrationWarning
    >
      <body
        className={`${cairo.className} ${locale} scroll-smooth antialiased`}
      >
        <LocaleProvider value={localeConfig}>
          <DictionaryProvider value={dict}>
            <AudioPlayingProvider>
              <ThemeProvider
                attribute="class"
                defaultTheme="system"
                enableSystem
                disableTransitionOnChange
              >
                <Header />
                <main className="relative">{children}</main>
                <footer className="py-60"></footer>
              </ThemeProvider>
              <AudioPlayer dir={localeConfig.dir} />
            </AudioPlayingProvider>
          </DictionaryProvider>
        </LocaleProvider>
      </body>
    </html>
  )
}
