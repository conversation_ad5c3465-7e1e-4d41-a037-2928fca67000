"use client"

import { But<PERSON> } from "./ui/button"

export function SurahShareButton(props: React.ComponentProps<typeof Button>) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          url: window.location.href,
        })
        // eslint-disable-next-line
      } catch (error) {
        console.error("خطأ أثناء المشاركة")
      }
    } else {
      alert("المشاركة غير مدعومة على هذا المتصفح.")
    }
  }

  return <Button onClick={handleShare} {...props} />
}
