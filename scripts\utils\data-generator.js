const path = require('path');

/**
 * Generate SEO description for a surah
 * @param {Object} surahData - Surah data with names
 * @param {Object} readerMeta - Reader metadata
 * @param {string} locale - Locale (en/ar)
 * @returns {string} SEO description
 */
function generateSeoDescription(surahData, readerMeta, locale) {
  const surahName = locale === 'ar' ? surahData.name_ar : surahData.name_en;
  const readerName = locale === 'ar' ? readerMeta.name_ar : readerMeta.name_en;
  
  if (locale === 'ar') {
    return `استماع وتحميل ${surahName} بصوت القارئ ${readerName}`;
  } else {
    return `Listen and download ${surahName} recited by ${readerName}`;
  }
}

/**
 * Generate SEO keywords for a surah
 * @param {Object} surahData - Surah data with names
 * @param {Object} readerMeta - Reader metadata
 * @param {string} locale - Locale (en/ar)
 * @returns {Array} Array of SEO keywords
 */
function generateSeoKeywords(surahData, readerMeta, locale) {
  const surahName = locale === 'ar' ? surahData.name_ar : surahData.name_en;
  const readerName = locale === 'ar' ? readerMeta.name_ar : readerMeta.name_en;
  
  if (locale === 'ar') {
    return [
      surahName,
      'القرآن',
      `السورة ${surahData.id}`,
      readerName
    ];
  } else {
    return [
      surahName,
      'Quran',
      `Surah ${parseInt(surahData.id)}`,
      readerName
    ];
  }
}

/**
 * Generate surah data array for a specific locale
 * @param {string} readerId - Reader ID
 * @param {Array} processedAudioFiles - Array of processed audio file objects
 * @param {string} locale - Locale (en/ar)
 * @param {Object} surahMap - Surah lookup map
 * @param {Object} readerMeta - Reader metadata
 * @returns {Array} Array of surah data objects
 */
function generateSurahData(readerId, processedAudioFiles, locale, surahMap, readerMeta) {
  const surahs = [];
  
  for (const audioFileData of processedAudioFiles) {
    // Skip files that failed processing
    if (!audioFileData.metadata) {
      console.warn(`⚠️  Skipping ${audioFileData.filename}: no metadata available`);
      continue;
    }
    
    // Extract surah ID from filename
    const basename = path.basename(audioFileData.filename, '.mp3');
    const surahId = basename.padStart(3, '0');
    
    // Look up surah data
    const surahData = surahMap[surahId];
    if (!surahData) {
      console.warn(`⚠️  Skipping ${audioFileData.filename}: no surah data found for ID ${surahId}`);
      continue;
    }
    
    // Create surah entry
    const surah = {
      id: surahId,
      name: locale === 'ar' ? surahData.name_ar : surahData.name_en,
      readerId: readerId,
      audioUrl: `/reader-files/${readerId}/surahs/${audioFileData.filename}`,
      audioLength: audioFileData.metadata.duration,
      audioFilesize: audioFileData.metadata.sizeKB,
      seoDescription: generateSeoDescription(
        { ...surahData, id: surahId },
        readerMeta,
        locale
      ),
      seoKeywords: generateSeoKeywords(
        { ...surahData, id: surahId },
        readerMeta,
        locale
      )
    };
    
    surahs.push(surah);
  }
  
  // Sort by surah ID
  surahs.sort((a, b) => a.id.localeCompare(b.id));
  
  console.log(`   Generated ${surahs.length} surah entries for ${locale} locale`);
  return surahs;
}

/**
 * Generate reader metadata for a specific locale
 * @param {string} readerId - Reader ID
 * @param {Object} readerMeta - Reader metadata from meta.json
 * @param {number} surahCount - Number of surahs available
 * @param {string} locale - Locale (en/ar)
 * @returns {Object} Reader metadata object
 */
function generateReaderMeta(readerId, readerMeta, surahCount, locale) {
  const readerName = locale === 'ar' ? readerMeta.name_ar : readerMeta.name_en;
  const seoDescription = locale === 'ar' ? readerMeta.seoDescription_ar : readerMeta.seoDescription_en;
  const seoKeywords = locale === 'ar' ? readerMeta.seoKeywords_ar : readerMeta.seoKeywords_en;
  
  return {
    id: readerId,
    name: readerName,
    surahsCount: surahCount,
    seoDescription: seoDescription || generateDefaultReaderDescription(readerName, locale),
    seoKeywords: seoKeywords || generateDefaultReaderKeywords(readerName, locale)
  };
}

/**
 * Generate default SEO description for a reader
 * @param {string} readerName - Reader name
 * @param {string} locale - Locale (en/ar)
 * @returns {string} Default SEO description
 */
function generateDefaultReaderDescription(readerName, locale) {
  if (locale === 'ar') {
    return `${readerName} هو قارئ شهير للقرآن. قرأ القرآن بأسلوبه المميز وصوته الجميل.`;
  } else {
    return `${readerName} is a famous reciter of the Quran. He has recited the Quran in a beautiful and melodious voice.`;
  }
}

/**
 * Generate default SEO keywords for a reader
 * @param {string} readerName - Reader name
 * @param {string} locale - Locale (en/ar)
 * @returns {Array} Default SEO keywords
 */
function generateDefaultReaderKeywords(readerName, locale) {
  if (locale === 'ar') {
    return [readerName, 'القرآن', 'التلاوة'];
  } else {
    return [readerName, 'Quran', 'Recitation'];
  }
}

/**
 * Generate complete data structure for a reader
 * @param {Object} readerInfo - Reader information object
 * @param {Array} processedAudioFiles - Array of processed audio file objects
 * @param {Object} surahMap - Surah lookup map
 * @param {Object} readerMeta - Reader metadata
 * @returns {Object} Complete data structure for both locales
 */
function generateCompleteReaderData(readerInfo, processedAudioFiles, surahMap, readerMeta) {
  const readerId = readerInfo.id;
  const surahCount = processedAudioFiles.filter(f => f.metadata !== null).length;
  
  console.log(`📝 Generating data for reader: ${readerId}`);
  
  // Generate data for both locales
  const data = {
    en: {
      readerMeta: generateReaderMeta(readerId, readerMeta, surahCount, 'en'),
      surahs: generateSurahData(readerId, processedAudioFiles, 'en', surahMap, readerMeta)
    },
    ar: {
      readerMeta: generateReaderMeta(readerId, readerMeta, surahCount, 'ar'),
      surahs: generateSurahData(readerId, processedAudioFiles, 'ar', surahMap, readerMeta)
    }
  };
  
  console.log(`✅ Generated complete data for ${readerId}: ${surahCount} surahs in 2 locales`);
  return data;
}

/**
 * Generate file paths for data output
 * @param {string} readerId - Reader ID
 * @param {string} locale - Locale (en/ar)
 * @returns {Object} Object with file paths
 */
function generateDataFilePaths(readerId, locale) {
  const basePath = path.join(process.cwd(), 'src', 'data', locale, readerId);
  
  return {
    readerMetaPath: path.join(basePath, 'reader-meta.json'),
    surahsPath: path.join(basePath, 'surahs.json'),
    basePath: basePath
  };
}

/**
 * Validate generated data structure
 * @param {Object} data - Generated data object
 * @returns {Object} Validation results
 */
function validateGeneratedData(data) {
  const issues = [];
  const warnings = [];
  
  // Check if data has both locales
  if (!data.en || !data.ar) {
    issues.push('Missing locale data (en or ar)');
  }
  
  for (const locale of ['en', 'ar']) {
    if (!data[locale]) continue;
    
    // Validate reader meta
    const readerMeta = data[locale].readerMeta;
    if (!readerMeta) {
      issues.push(`Missing reader metadata for ${locale}`);
    } else {
      if (!readerMeta.id || !readerMeta.name) {
        issues.push(`Invalid reader metadata for ${locale}: missing id or name`);
      }
      if (readerMeta.surahsCount <= 0) {
        warnings.push(`No surahs found for ${locale}`);
      }
    }
    
    // Validate surahs
    const surahs = data[locale].surahs;
    if (!Array.isArray(surahs)) {
      issues.push(`Invalid surahs data for ${locale}: not an array`);
    } else {
      for (const surah of surahs) {
        if (!surah.id || !surah.name || !surah.audioUrl) {
          issues.push(`Invalid surah entry for ${locale}: missing required fields`);
        }
        if (surah.audioLength <= 0) {
          warnings.push(`Invalid audio length for surah ${surah.id} in ${locale}`);
        }
        if (surah.audioFilesize <= 0) {
          warnings.push(`Invalid file size for surah ${surah.id} in ${locale}`);
        }
      }
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues: issues,
    warnings: warnings
  };
}

module.exports = {
  generateSeoDescription,
  generateSeoKeywords,
  generateSurahData,
  generateReaderMeta,
  generateCompleteReaderData,
  generateDataFilePaths,
  validateGeneratedData
};
