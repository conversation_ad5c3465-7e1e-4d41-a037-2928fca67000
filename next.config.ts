import type { NextConfig } from "next"

const nextConfig: NextConfig = {
  output: "standalone",

  async headers() {
    return [
      {
        source: "/:path*{/}?",
        headers: [
          {
            key: "X-Accel-Buffering",
            value: "no",
          },
        ],
      },
    ]
  },

  images: {
    // Configure image optimization if needed
    formats: ["image/webp"],
  },
}

export default nextConfig
