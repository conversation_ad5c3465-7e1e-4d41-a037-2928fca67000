"use client"

import * as React from "react"
import { usePathname, useRouter } from "next/navigation"

import { i18nConfig } from "@/config/i18n"
import { useDictionary } from "@/hooks/useDictionary"
import { useLocale } from "@/hooks/useLocale"

import { Button } from "./ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"

interface LanguageSwitcherProps {
  className?: string
}

export function LanguageSwitcher({ className }: LanguageSwitcherProps) {
  const dict = useDictionary().layout.nav
  const router = useRouter()
  const locale = useLocale()
  const pathname = usePathname()

  const locales = i18nConfig.locales

  const handleLanguageChange = (newLocale: string) => {
    // Remove current locale from pathname
    
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, "") || "/"
    
    // Create new path with new locale
    const newPath = `/${newLocale}${pathWithoutLocale}`
    console.log(newPath);

    // Set cookie for language preference
    document.cookie = `${i18nConfig.localeCookie}=${newLocale}; path=/; max-age=${365 * 24 * 60 * 60}; SameSite=Lax`

    // Navigate to new path
    router.push(newPath, { scroll: false })
  }

  return (
    <DropdownMenu modal={false} dir={locale.dir}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className={className}>
          <span className="truncate">{locale.name}</span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuLabel>{dict.languageSwitcherLabel}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuRadioGroup
          value={locale.code}
          onValueChange={handleLanguageChange}
        >
          {locales.map((locale) => (
            <DropdownMenuRadioItem value={locale.code} key={locale.code}>
              {locale.name}
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
