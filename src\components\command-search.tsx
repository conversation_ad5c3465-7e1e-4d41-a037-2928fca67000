import * as React from "react"

import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandList,
} from "@/components/ui/command"

export default function CommandSearch({
  children,
  dialogTrigger,
  commandEmptyLabel,
  commandInputPlaceholder,
}: {
  children?: React.ReactNode
  dialogTrigger: React.JSX.Element
  commandEmptyLabel?: string
  commandInputPlaceholder?: string
}) {
  return (
    <CommandDialog dialogTrigger={dialogTrigger}>
      <CommandInput placeholder={commandInputPlaceholder} />
      <CommandList>
        <CommandEmpty>{commandEmptyLabel}</CommandEmpty>
        <CommandGroup heading="Surahs">{children}</CommandGroup>
      </CommandList>
    </CommandDialog>
  )
}
