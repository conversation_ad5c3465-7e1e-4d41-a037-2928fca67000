"use client"

import React from "react"

import { cn } from "@/lib/utils"
import { useAudioPlaying } from "@/hooks/useAudioPlaying"

import { AudioPlayingIcon } from "./ui/icons"

export function PlayingIndicator({
  audioUrl,
  className,
}: {
  audioUrl: string
  className?: string
}) {
  const { playing, isStopped } = useAudioPlaying()

  if (playing?.sura.audioUrl !== audioUrl) return null

  return (
    <>
      <AudioPlayingIcon isStopped={isStopped} className={cn("", className)} />
    </>
  )
}
