import React from "react"

export function AudioPlayingIcon({
  className,
  isStopped,
}: {
  className?: string
  isStopped?: boolean
}) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
      className={className}
      key={String(isStopped)}
    >
      <rect width={2.8} height={12} x={1} y={6} fill="#2b7fff">
        <animate
          attributeName="y"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.42s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="6;1;6"
        ></animate>
        <animate
          attributeName="height"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.42s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="12;22;12"
        ></animate>
      </rect>
      <rect width={2.8} height={12} x={5.8} y={6} fill="#2b7fff">
        <animate
          attributeName="y"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.21s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="6;1;6"
        ></animate>
        <animate
          attributeName="height"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.21s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="12;22;12"
        ></animate>
      </rect>
      <rect width={2.8} height={12} x={10.6} y={6} fill="#2b7fff">
        <animate
          id="svgSpinnersBarsScaleMiddle0"
          attributeName="y"
          begin={
            isStopped
              ? "indefinite"
              : "0;svgSpinnersBarsScaleMiddle1.end-0.105s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="6;1;6"
        ></animate>
        <animate
          attributeName="height"
          begin={
            isStopped
              ? "indefinite"
              : "0;svgSpinnersBarsScaleMiddle1.end-0.105s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="12;22;12"
        ></animate>
      </rect>
      <rect width={2.8} height={12} x={15.4} y={6} fill="#2b7fff">
        <animate
          attributeName="y"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.21s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="6;1;6"
        ></animate>
        <animate
          attributeName="height"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.21s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="12;22;12"
        ></animate>
      </rect>
      <rect width={2.8} height={12} x={20.2} y={6} fill="#2b7fff">
        <animate
          id="svgSpinnersBarsScaleMiddle1"
          attributeName="y"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.42s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="6;1;6"
        ></animate>
        <animate
          attributeName="height"
          begin={
            isStopped ? "indefinite" : "svgSpinnersBarsScaleMiddle0.begin+0.42s"
          }
          calcMode="spline"
          dur="0.63s"
          keySplines=".14,.73,.34,1;.65,.26,.82,.45"
          values="12;22;12"
        ></animate>
      </rect>
    </svg>
  )
}

export function ToggleThemeIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="size-4.5"
    >
      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
      <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"></path>
      <path d="M12 3l0 18"></path>
      <path d="M12 9l4.65 -4.65"></path>
      <path d="M12 14.3l7.37 -7.37"></path>
      <path d="M12 19.6l8.85 -8.85"></path>
    </svg>
  )
}
