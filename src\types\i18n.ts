import type { i18nConfig } from "@/config/i18n"
import type { getDictionary } from "@/lib/i18n"

/**
 * Type for the supported locales
 */
type Locale = "ar" | "en"

/**
 * Type for the dictionary returned by getDictionary
 */
type GetDictionary = Awaited<ReturnType<typeof getDictionary>>

/**
 * Type for the i18n config
 */
type i18nConfig = typeof i18nConfig

type LocaleConfig = i18nConfig["locales"][number]

export type { Locale, i18nConfig, GetDictionary,LocaleConfig }
