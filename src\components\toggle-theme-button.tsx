"use client"

import React from "react"
import { useTheme } from "next-themes"

import { But<PERSON> } from "./ui/button"
import { ToggleThemeIcon } from "./ui/icons"

export default function ToggleThemeButton({
  className,
}: {
  className?: string
}) {
  const { setTheme, theme } = useTheme()
  return (
    <Button
      className={className}
      variant="outline"
      size="icon"
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
    >
      <ToggleThemeIcon />
    </Button>
  )
}
