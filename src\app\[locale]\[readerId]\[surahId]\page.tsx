import * as React from "react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { ChevronLeft, ChevronsLeft, Download, Share2 } from "lucide-react"

import { Locale } from "@/types/i18n"
import { getDictionary } from "@/lib/i18n"
import { getReaderById } from "@/lib/reciters"
import { getAllSurahsByReader, getSurahByIdReader } from "@/lib/surahs"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { AudioPlayButton } from "@/components/audio-play-button"
import { SurahShareButton } from "@/components/surah-share-button"

type Props = {
  params: Promise<{ locale: Locale; readerId: string; surahId: string }>
}

export async function generateStaticParams({
  params,
}: {
  params: { readerId: string }
}) {
  const { readerId } = params
  const surahs = getAllSurahsByReader("ar", readerId)

  return surahs.map((surah) => ({
    surahId: surah.id,
  }))
}

export default async function SurahPage({ params }: Props) {
  const { locale, readerId, surahId } = await params
  const surah = getSurahByIdReader(locale, readerId, surahId)
  const reader = getReaderById(locale, readerId)
  const dict = (await getDictionary(locale)).surah

  if (!surah || !reader) notFound()

  return (
    <div className="px-4 pt-10 text-center">
      <div className="flex w-full flex-col gap-2 *:justify-between sm:flex-row">
        <Button asChild variant="secondary">
          <Link href={`/${locale}/${readerId}#${surahId}`}>
            {dict.backToSurahs}
            <ChevronLeft className={locale !== "ar" ? "rotate-180" : ""} />
          </Link>
        </Button>
        <Button asChild variant="secondary">
          <Link href={`/${locale}`}>
            {dict.backToReaders}
            <ChevronsLeft className={locale !== "ar" ? "rotate-180" : ""} />
          </Link>
        </Button>
      </div>
      <Separator className="my-8" />
      <div>
        {/* Title */}
        <div>
          <h1 className="font-sans">{surah.name}</h1>
          <p>{reader.name}</p>
        </div>
        {/* Share And Download Buttons */}
        <div className="mt-6 flex justify-center gap-2">
          <Button asChild size="icon" variant="outline">
            <a
              aria-label="Download"
              href={`${surah.audioUrl}`}
              download={`${surah.name}-${reader.name}.mp3`}
            >
              <span className="sr-only">Download</span>
              <Download />
            </a>
          </Button>
          <SurahShareButton size="icon" variant="outline">
            <Share2 />
            <span className="sr-only">Share</span>
          </SurahShareButton>
        </div>

        {/* Play Audio Button */}
        <AudioPlayButton
          className="mt-8 w-full"
          size="lg"
          sura={surah}
          reader={reader}
          playLabel={dict.play}
          stopLabel={dict.pause}
        />
      </div>
    </div>
  )
}
