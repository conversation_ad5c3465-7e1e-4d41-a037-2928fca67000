const fs = require('fs');
const path = require('path');

/**
 * Load and parse the surah metadata from public/surahs-meta.json
 * @returns {Promise<Array>} Array of surah metadata objects
 */
async function loadSurahMetadata() {
  const surahMetaPath = path.join(process.cwd(), 'public', 'surahs-meta.json');
  
  try {
    console.log('📖 Loading surah metadata from public/surahs-meta.json...');
    
    if (!fs.existsSync(surahMetaPath)) {
      throw new Error(`Surah metadata file not found: ${surahMetaPath}`);
    }
    
    const rawData = fs.readFileSync(surahMetaPath, 'utf8');
    const surahsArray = JSON.parse(rawData);
    
    // Validate the structure
    if (!Array.isArray(surahsArray)) {
      throw new Error('Surah metadata must be an array');
    }
    
    // Validate each surah entry
    for (const surah of surahsArray) {
      if (!surah.id || !surah.name_ar || !surah.name_en) {
        throw new Error(`Invalid surah entry: ${JSON.stringify(surah)}. Must have id, name_ar, and name_en`);
      }
    }
    
    console.log(`✅ Successfully loaded ${surahsArray.length} surahs from metadata file`);
    return surahsArray;
    
  } catch (error) {
    console.error('❌ Error loading surah metadata:', error.message);
    throw error;
  }
}

/**
 * Create a lookup map from surah array for O(1) access
 * @param {Array} surahsArray - Array of surah metadata objects
 * @returns {Object} Object with surah IDs as keys
 */
function createSurahLookupMap(surahsArray) {
  const surahMap = {};
  
  for (const surah of surahsArray) {
    surahMap[surah.id] = {
      name_ar: surah.name_ar,
      name_en: surah.name_en
    };
  }
  
  console.log(`📋 Created surah lookup map with ${Object.keys(surahMap).length} entries`);
  return surahMap;
}

/**
 * Extract surah ID from audio filename
 * @param {string} filename - Audio filename (e.g., "001.mp3")
 * @returns {string|null} Surah ID (e.g., "001") or null if invalid
 */
function extractSurahIdFromFilename(filename) {
  const basename = path.basename(filename, '.mp3');
  
  // Check if it matches the expected pattern (3 digits)
  if (!/^\d{3}$/.test(basename)) {
    return null;
  }
  
  return basename;
}

/**
 * Map audio file to surah data
 * @param {string} audioFilePath - Path to audio file
 * @param {Object} surahMap - Surah lookup map
 * @returns {Object|null} Surah data or null if not found
 */
function mapAudioFileToSurah(audioFilePath, surahMap) {
  const filename = path.basename(audioFilePath);
  const surahId = extractSurahIdFromFilename(filename);
  
  if (!surahId) {
    console.warn(`⚠️  Invalid filename format: ${filename} (expected: XXX.mp3)`);
    return null;
  }
  
  const surahData = surahMap[surahId];
  if (!surahData) {
    console.warn(`⚠️  Audio file ${filename} has no corresponding entry in surahs-meta.json`);
    return null;
  }
  
  return {
    id: surahId,
    ...surahData
  };
}

/**
 * Validate audio file coverage against surah metadata
 * @param {Array} audioFiles - Array of audio file paths
 * @param {Object} surahMap - Surah lookup map
 * @returns {Object} Validation results
 */
function validateAudioFileCoverage(audioFiles, surahMap) {
  const foundSurahs = new Set();
  const invalidFiles = [];
  const validFiles = [];
  
  // Check each audio file
  for (const audioFile of audioFiles) {
    const filename = path.basename(audioFile);
    const surahId = extractSurahIdFromFilename(filename);
    
    if (!surahId) {
      invalidFiles.push(filename);
      continue;
    }
    
    if (surahMap[surahId]) {
      foundSurahs.add(surahId);
      validFiles.push(audioFile);
    } else {
      console.warn(`⚠️  Audio file ${filename} has no corresponding metadata entry`);
    }
  }
  
  // Check for missing audio files
  const allSurahIds = Object.keys(surahMap);
  const missingSurahs = allSurahIds.filter(id => !foundSurahs.has(id));
  
  const results = {
    totalSurahs: allSurahIds.length,
    foundSurahs: foundSurahs.size,
    missingSurahs: missingSurahs,
    invalidFiles: invalidFiles,
    validFiles: validFiles
  };
  
  // Log validation results
  console.log(`📊 Validation Results:`);
  console.log(`   Total surahs in metadata: ${results.totalSurahs}`);
  console.log(`   Audio files found: ${results.foundSurahs}`);
  
  if (results.missingSurahs.length > 0) {
    console.log(`   Missing audio files: ${results.missingSurahs.length}`);
    if (results.missingSurahs.length <= 10) {
      console.log(`   Missing: ${results.missingSurahs.join(', ')}`);
    }
  }
  
  if (results.invalidFiles.length > 0) {
    console.log(`   Invalid filenames: ${results.invalidFiles.length}`);
    console.log(`   Invalid: ${results.invalidFiles.join(', ')}`);
  }
  
  return results;
}

module.exports = {
  loadSurahMetadata,
  createSurahLookupMap,
  extractSurahIdFromFilename,
  mapAudioFileToSurah,
  validateAudioFileCoverage
};
