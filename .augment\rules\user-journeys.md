---
type: "always_apply"
---

# User Journey Maps – Quran Reciters Website

This document outlines the key user journeys on the website, aiming to improve the user experience and ensure that functional requirements are met in a practical manner.

---

## Journey 1: First-Time Visit

**Goal:** Browse the website and select a reciter to listen to.

**Steps:**

1. The user visits the website for the first time.
2. The browser language is automatically detected (e.g., Arabic).
3. A button to change the language is displayed, if desired.
4. The homepage displays a list of reciters.

**Points of Interest:**

- The selected language should be saved in cookies for future visits.

---

## Journey 2: Changing the Language

**Goal:** Browse the website in the preferred language.

**Steps:**

1. The user sees a language switch button in the page header.
2. They select another language (e.g., English).
3. All texts on the website change to the selected language.
4. The user’s language choice is saved in cookies for future use.

**Points of Interest:**

- The user should not lose their current location on the website after changing the language.
- There should be a mechanism to automatically display the website in the preferred language if no manual language selection has been made.

---

## Journey 3: Selecting a Reciter

**Goal:** Browse reciters and choose one.

**Steps:**

1. The user sees a list of reciter cards on the homepage.
2. They can search by name or scroll to find a reciter.
3. They click the "Listen to Recitations" button on a reciter’s card.
4. They are taken to the surah list page for that reciter.

**Points of Interest:**

- Instant search results should be supported while typing.

---

## Journey 4: Searching for and Playing a Surah

**Goal:** Find a specific surah and listen to it.

**Steps:**

1. The user is on a reciter’s surah list page.
2. They use the search field or scroll to locate a surah.
3. A "Play" button and a "Download" button are displayed next to each surah.
4. They click the "Play" button next to the surah.
5. They are taken to a dedicated surah page to listen.

**Points of Interest:**

- Instant search results should be supported while typing.

---

## Journey 5: Listening to a Surah

**Goal:** Listen to a specific surah.

**Steps:**

1. The user is on a specific surah page.
2. An audio player is displayed at the bottom of the page.
3. They click the "Play" button in the player.
4. The surah starts playing immediately.

**Points of Interest:**

- The audio player should remain fixed at the bottom of the screen.

---

## Journey 6: Downloading a Surah

**Goal:** Download the MP3 file of a surah.

**Steps:**

1. On the reciter’s page or the surah page.
2. The user clicks the "Download" button next to the surah.
3. The file download begins immediately.

**Points of Interest:**

- The file name should be clear and include the surah name and reciter name.

---

## Journey 7: Switching Reciters from the Reciter or Surah Page

**Goal:** Switch to another reciter for the same surah or on the same page.

**Steps:**

1. The user is on a specific surah page.
2. They open the reciter list from the header or sidebar.
3. They select a new reciter.
4. The page transitions directly to the same surah with the new reciter’s audio.

**Points of Interest:**

- The current surah should remain open when switching reciters.
- The audio player should automatically update with the new reciter’s data.

---

## Journey 8: Switching Surahs from the Surah Page

**Goal:** Switch to another surah with the same reciter.

**Steps:**

1. The user is on a specific surah page.
2. They see a list of surahs for the current reciter.
3. They select a new surah.
4. The page transitions directly to the new surah with the same reciter’s audio.

**Points of Interest:**

- The current reciter should remain the same when switching surahs.
- The audio player should automatically update with the new surah’s data.

---

## Journey 9: Accessing from a Search Engine

**Goal:** Land directly on a reciter or surah page via Google.

**Steps:**

1. The user searches on Google: "Surah Al-Kahf by Mishary Alafasy".
2. A result from our website appears.
3. The user clicks the link and is taken directly to the surah page.

**Points of Interest:**

- SEO should be supported via dynamic meta tags.
- The page title should include the surah name, reciter name, and appropriate language.
