import * as React from "react"
import { NextRequest } from "next/server"
import { match as matchLocale } from "@formatjs/intl-localematcher"
import Negotiator from "negotiator"

import { Locale } from "@/types/i18n"
import { i18nConfig } from "@/config/i18n"

const dictionaries = {
  en: () => import("@/locales/en.json").then((module) => module.default),
  ar: () => import("@/locales/ar.json").then((module) => module.default),
}

/**
 * Get the dictionary for the given locale
 */
export const getDictionary = React.cache(async (locale: Locale) =>
  dictionaries[locale]()
)

/**
 * Get the preferred locale from the request headers
 */
export function getLocaleFromHeaders(request: NextRequest) {
  if (request.cookies.has(i18nConfig.localeCookie)) {
    return request.cookies.get(i18nConfig.localeCookie)!.value as Locale
  }

  const negotiatorHeaders: Record<string, string> = {}
  request.headers.forEach((value, key) => (negotiatorHeaders[key] = value))

  const locales = i18nConfig.locales.map((locale) => locale.code)
  const languages = new Negotiator({ headers: negotiatorHeaders }).languages()

  const locale = matchLocale(languages, locales, i18nConfig.defaultLocale)
  return locale
}

/**
 * Check if the pathname is missing the locale
 */
export const pathnameIsMissingLocale = (pathname: string) => {
  return i18nConfig.locales.every(
    (locale) =>
      !pathname.startsWith(`/${locale.code}/`) && pathname !== `/${locale.code}`
  )
}

/**
 * Get the locale configuration object for the given locale code
 */
export function getLocaleConfig(locale: Locale) {
  return i18nConfig.locales.find((l) => l.code === locale)!
}
