<div dir="rtl">

# تطبيق قراء القرآن الكريم

تطبيق ويب متطور لاستماع وتحميل تلاوات القرآن الكريم بأصوات مختلف القراء المشهورين. مبني باستخدام Next.js 15 مع TypeScript ودعم كامل للغتين العربية والإنجليزية.

## 🌟 المميزات

- **دعم متعدد اللغات**: واجهة باللغتين العربية والإنجليزية
- **مشغل صوتي متقدم**: تحكم كامل في التشغيل مع إمكانية التحكم الخارجي
- **تصميم متجاوب**: يعمل بسلاسة على جميع الأجهزة
- **بحث ذكي**: البحث في القراء والسور بسهولة
- **تحسين محركات البحث**: SEO محسن لكل قارئ وسورة
- **أداء عالي**: تحميل سريع وتجربة مستخدم سلسة

## 🚀 البدء السريع

### متطلبات النظام

- Node.js 18.0 أو أحدث
- npm أو yarn أو pnpm

### تشغيل المشروع محلياً

1. **استنساخ المشروع**:

```bash
git clone https://github.com/abdooo097/reciters-quran.git
cd reciters-quran
```

2. **تثبيت التبعيات**:

```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **تشغيل الخادم المحلي**:

```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

4. **فتح التطبيق**: افتح [http://localhost:3000](http://localhost:3000) في المتصفح

## 📁 هيكل المشروع

```
reciters-quran/
├── public/
│   └── reader-files/           # ملفات القراء الصوتية والبيانات
│       └── [readerId]/
│           ├── meta.json       # بيانات القارئ الأساسية
│           └── surahs/         # ملفات السور الصوتية
│               ├── 001.mp3
│               ├── 002.mp3
│               └── ...
├── src/
│   ├── data/                   # بيانات التطبيق المنظمة
│   │   ├── ar/                 # البيانات العربية
│   │   │   └── [readerId]/
│   │   │       ├── reader-meta.json
│   │   │       └── surahs.json
│   │   └── en/                 # البيانات الإنجليزية
│   │       └── [readerId]/
│   │           ├── reader-meta.json
│   │           └── surahs.json
│   ├── components/             # مكونات React
│   ├── lib/                    # وظائف مساعدة
│   ├── types/                  # تعريفات TypeScript
│   └── hooks/                  # React Hooks مخصصة
└── scripts/                    # سكريبتات المساعدة
    └── generate-audio-metadata.js
```

## 📋 دليل إضافة قارئ جديد

### الخطوة 1: إعداد مجلد القارئ

1. **إنشاء مجلد القارئ**:

```bash
mkdir public/reader-files/[reader-id]
mkdir public/reader-files/[reader-id]/surahs
```

2. **إضافة ملفات الصوت**:
   - ضع ملفات MP3 في مجلد `surahs/`
   - استخدم تسمية موحدة: `001.mp3`, `002.mp3`, إلخ
   - تأكد من جودة الصوت وحجم الملف المناسب

### الخطوة 2: إنشاء ملف البيانات الأساسية

أنشئ ملف `public/reader-files/[reader-id]/meta.json`:

```json
{
  "name_en": "Reader Name in English",
  "name_ar": "اسم القارئ بالعربية",
  "seoDescription_en": "Listen and download the Holy Quran in the voice of [Reader Name].",
  "seoDescription_ar": "استماع وتحميل سور القرآن الكريم بصوت القارئ [اسم القارئ].",
  "seoKeywords_en": ["Reader Name", "Quran", "Surah", "Recitation"],
  "seoKeywords_ar": ["اسم القارئ", "القرآن", "سورة", "تلاوة"]
}
```

### الخطوة 3: إنشاء هيكل البيانات المحلية

1. **إنشاء مجلدات البيانات**:

```bash
mkdir src/data/ar/[reader-id]
mkdir src/data/en/[reader-id]
```

2. **ملف البيانات العربية** `src/data/ar/[reader-id]/reader-meta.json`:

```json
{
  "id": "reader-id",
  "name": "اسم القارئ بالعربية",
  "surahsCount": 0,
  "seoDescription": "استماع وتحميل سور القرآن الكريم بصوت القارئ [اسم القارئ].",
  "seoKeywords": ["اسم القارئ", "القرآن", "سورة", "تلاوة"]
}
```

3. **ملف البيانات الإنجليزية** `src/data/en/[reader-id]/reader-meta.json`:

```json
{
  "id": "reader-id",
  "name": "Reader Name in English",
  "surahsCount": 0,
  "seoDescription": "Listen and download the Holy Quran in the voice of [Reader Name].",
  "seoKeywords": ["Reader Name", "Quran", "Surah", "Recitation"]
}
```

### الخطوة 4: توليد البيانات التلقائي

استخدم السكريبت المدمج لتوليد بيانات السور تلقائياً:

```bash
# توليد البيانات لقارئ محدد
npm run generate-metadata -- --reader [reader-id]

# توليد البيانات مع النسخ الاحتياطي
npm run generate-metadata:backup -- --reader [reader-id]

# معاينة التغييرات بدون تطبيق
npm run generate-metadata:dry-run -- --reader [reader-id]

# توليد البيانات مع تفاصيل إضافية
npm run generate-metadata:verbose -- --reader [reader-id]
```

### الخطوة 5: التحقق من البيانات

بعد تشغيل السكريبت، تأكد من إنشاء الملفات التالية:

- `src/data/ar/[reader-id]/surahs.json`
- `src/data/en/[reader-id]/surahs.json`

### الخطوة 6: اختبار القارئ الجديد

1. **تشغيل الخادم المحلي**:

```bash
npm run dev
```

2. **التحقق من ظهور القارئ** في قائمة القراء
3. **اختبار تشغيل السور** والتأكد من عمل الروابط
4. **فحص البيانات** في كلا اللغتين

## 🛠️ الأوامر المفيدة

```bash
# تشغيل وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل المشروع في وضع الإنتاج
npm start

# فحص الكود
npm run lint

# توليد بيانات جميع القراء
npm run generate-metadata

# تنظيف المشروع
npm run cleanup
```

## 🔧 التكوين المتقدم

### إعدادات Next.js

يمكن تعديل إعدادات Next.js في ملف `next.config.ts`:

- تحسين الصور
- إعدادات الخادم
- تكوين الهيدرز

### إعدادات TypeScript

تكوين TypeScript في `tsconfig.json` يتضمن:

- مسارات مختصرة (`@/*`)
- إعدادات صارمة للتطوير
- دعم JSX

## 📱 الدعم والمساهمة

### الإبلاغ عن المشاكل

إذا واجهت أي مشكلة، يرجى:

1. التحقق من [قائمة المشاكل المعروفة](https://github.com/abdooo097/reciters-quran/issues)
2. إنشاء تقرير مشكلة جديد مع التفاصيل الكاملة

### المساهمة في المشروع

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات مع الاختبارات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🙏 شكر وتقدير

- فريق Next.js لإطار العمل الرائع
- مجتمع React للأدوات والمكتبات
- جميع المساهمين في المشروع

---

**ملاحظة**: تأكد من اتباع الخطوات بالترتيب المحدد لضمان عمل القارئ الجديد بشكل صحيح.
