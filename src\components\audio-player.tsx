"use client"

import * as React from "react"
import Link from "next/link"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"
import { useAudioPlaying } from "@/hooks/useAudioPlaying"
import { useLocale } from "@/hooks/useLocale"
import {
  MediaPlayer,
  MediaPlayerAudio,
  MediaPlayerControls,
  MediaPlayerLoop,
  MediaPlayerPlay,
  MediaPlayerPlaybackSpeed,
  MediaPlayerSeek,
  MediaPlayerSeekBackward,
  MediaPlayerSeekForward,
  MediaPlayerVolume,
} from "@/components/ui/media-player"

import { Button } from "./ui/button"

export default function AudioPlayer({ dir }: { dir?: "ltr" | "rtl" }) {
  const locale = useLocale()
  const { playing, setIsStopped, setPlaying, playButtonRef } = useAudioPlaying()
  const isPlayerVisible = !!playing

  return (
    <div
      className={cn(
        "fixed bottom-0 z-30 w-full duration-300",
        isPlayerVisible
          ? "animate-in fade-in slide-in-from-bottom block"
          : "animate-out slide-out-to-bottom fade-out hidden transition-discrete"
      )}
    >
      <MediaPlayer
        onPlay={() => setIsStopped(false)}
        onPause={() => setIsStopped(true)}
        className="dark:bg-sidebar/90 bg-background/90 h-33 rounded-t-3xl rounded-b-none border-x-1 border-t-4 pt-2 shadow-lg backdrop-blur-lg dark:border-t-1 dark:shadow-[0_-2px_15px_#00000060]"
      >
        <div dir={dir} className="flex items-center justify-between px-4">
          <Link
            href={`/${locale.code}/${playing?.sura.readerId}#${playing?.sura.id}`}
            className="text-foreground grow truncate font-sans"
          >
            {playing?.sura.name} - {playing?.reader.name}
          </Link>
          <Button
            className="-me-2.5"
            variant="ghost"
            size="icon"
            onClick={() => {
              setIsStopped(true)
              setPlaying(null)
            }}
          >
            <X />
          </Button>
        </div>
        <MediaPlayerAudio
          playsInline
          autoPlay
          key={playing?.sura.audioUrl}
          onEnded={() => setIsStopped(true)}
          className="sr-only"
        >
          <source src={playing?.sura.audioUrl} type="audio/mp3" />
        </MediaPlayerAudio>
        <MediaPlayerControls className="flex-col items-start gap-2.5">
          <MediaPlayerSeek withTime />
          <div className="flex w-full items-center justify-center gap-2">
            <MediaPlayerSeekBackward />
            <MediaPlayerPlay ref={playButtonRef} />
            <MediaPlayerSeekForward />
            <MediaPlayerVolume expandable />
            <MediaPlayerPlaybackSpeed />
            <MediaPlayerLoop />
          </div>
        </MediaPlayerControls>
      </MediaPlayer>
    </div>
  )
}
