"use client"

// useDictionary.tsx

import * as React from "react"

import { GetDictionary } from "@/types/i18n"

const DictionaryContext = React.createContext<GetDictionary | null>(null!)

export function DictionaryProvider({
  children,
  value,
}: {
  children: React.ReactNode
  value: GetDictionary
}) {
  return (
    <DictionaryContext.Provider value={value}>
      {children}
    </DictionaryContext.Provider>
  )
}

export function useDictionary() {
  const context = React.useContext(DictionaryContext)
  if (!context) {
    throw new Error("useDictionary must be used within a DictionaryProvider")
  }
  return context
}
