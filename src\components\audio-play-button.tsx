"use client"

import React from "react"
import { Pause, Play } from "lucide-react"

import { Reader, Sura } from "@/types/data"
import { useAudioPlaying } from "@/hooks/useAudioPlaying"
import { Button } from "@/components/ui/button"

export function AudioPlayButton({
  sura,
  reader,
  className,
  size = "icon",
  stopLabel,
  playLabel,
}: {
  sura: Sura
  reader: Reader
  className?: string
  size?: "icon" | "sm" | "lg"
  stopLabel?: string
  playLabel?: string
}) {
  const { playing, setPlaying, playButtonRef, isStopped } = useAudioPlaying()
  const isPlaying = playing?.sura.audioUrl === sura.audioUrl

  return (
    <Button
      size={size}
      className={className}
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()

        if (isPlaying) {
          playButtonRef.current.click()
        } else {
          setPlaying({ reader, sura })
        }
      }}
    >
      <span className="sr-only">audio button</span>
      {isStopped ? (
        <>
          <Play /> {playLabel}
        </>
      ) : isPlaying ? (
        <>
          <Pause /> {stopLabel}
        </>
      ) : (
        <>
          <Play /> {playLabel}
        </>
      )}
    </Button>
  )
}
