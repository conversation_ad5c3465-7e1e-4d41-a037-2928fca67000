{"name": "reciters-quran", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build && npm run copy", "start": "next start", "lint": "next lint", "cleanup": "rm -rf node_modules", "copy": "node scripts/copyFiles.js", "generate-metadata": "node scripts/generate-audio-metadata.js", "generate-metadata:dry-run": "node scripts/generate-audio-metadata.js --dry-run", "generate-metadata:backup": "node scripts/generate-audio-metadata.js --backup", "generate-metadata:verbose": "node scripts/generate-audio-metadata.js --verbose --backup"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@types/negotiator": "^0.6.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "fuse.js": "^7.1.0", "lucide-react": "^0.525.0", "media-chrome": "^4.11.1", "music-metadata": "^11.5.0", "negotiator": "^1.0.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^10.1.5", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}