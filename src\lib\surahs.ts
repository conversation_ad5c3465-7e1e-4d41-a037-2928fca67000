import fs from "fs"
import path from "path"
import * as React from "react"

import { Sura } from "@/types/data"
import { Locale } from "@/types/i18n"

/**
 * Get all surah data for a specific reciter and locale
 *
 * This function reads from the JSON file located at:
 * `src/data/{locale}/{readerId}/surahs.json`
 *
 * @param locale - The locale ("en" or "ar")
 * @param readerId - The unique identifier of the Quran reciter
 * @returns Array of Sura objects or empty array if error/not found
 *
 * @example
 * ```typescript
 * // Get English surahs for husary reciter
 * const surahs = getSurahsByReaderAndLocale("en", "husary");
 * console.log(surahs.length); // Number of surahs available
 *
 * // Get Arabic surahs for mishary reciter
 * const arabicSurahs = getSurahsByReaderAndLocale("ar", "mishary");
 * ```
 */
export const getAllSurahsByReader = React.cache(
  (locale: Locale, readerId: string): Sura[] => {
    // Validate locale parameter
    if (!locale || (locale !== "ar" && locale !== "en")) {
      console.error(`Invalid locale: ${locale}. Must be 'ar' or 'en'`)
      return []
    }

    // Validate readerId parameter
    if (!readerId || typeof readerId !== "string" || readerId.trim() === "") {
      console.error(`Invalid readerId: ${readerId}. Must be a non-empty string`)
      return []
    }

    // Construct the file path: src/data/{locale}/{readerId}/surahs.json
    const surahsPath = path.join(
      process.cwd(),
      "src",
      "data",
      locale,
      readerId,
      "surahs.json"
    )

    // Check if the file exists
    if (!fs.existsSync(surahsPath)) {
      console.warn(
        `Surahs file not found for reader ${readerId} in locale ${locale}: ${surahsPath}`
      )
      return []
    }

    try {
      // Read and parse the JSON file
      const surahsData = fs.readFileSync(surahsPath, "utf8")
      const surahs: Sura[] = JSON.parse(surahsData)

      // Validate the data structure
      if (!Array.isArray(surahs)) {
        console.error(
          `Invalid surahs data structure for ${readerId} in locale ${locale}: expected array`
        )
        return []
      }

      // Validate each surah object
      const validSurahs = surahs.filter((surah, index) => {
        if (!isValidSura(surah)) {
          console.warn(
            `Invalid surah data at index ${index} for ${readerId} in locale ${locale}`
          )
          return false
        }
        return true
      })

      // Sort surahs by ID for consistent ordering
      return validSurahs.sort((a, b) => a.id.localeCompare(b.id))
    } catch (error) {
      console.error(
        `Error reading surahs data for ${readerId} in locale ${locale}:`,
        error
      )
      return []
    }
  }
)

/**
 * Get a specific surah by ID for a reciter and locale
 * @param locale - The locale ("en" or "ar")
 * @param readerId - The unique identifier of the Quran reciter
 * @param surahId - The ID of the surah (e.g., "001", "002")
 * @returns Sura object or null if not found
 */
export const getSurahByIdReader = React.cache(
  (locale: Locale, readerId: string, surahId: string): Sura | null => {
    const surahs = getAllSurahsByReader(locale, readerId)
    
    if (surahs.length === 0) {
      return null
    }

    const surah = surahs.find(s => s.id === surahId)
    return surah || null
  }
)

/**
 * Validate surah data structure
 * @param surah - The surah object to validate
 * @returns True if the surah object is valid
 */
function isValidSura(surah: any): surah is Sura {
  return (
    surah &&
    typeof surah === "object" &&
    typeof surah.id === "string" &&
    typeof surah.name === "string" &&
    typeof surah.readerId === "string" &&
    typeof surah.audioUrl === "string" &&
    typeof surah.audioLength === "number" &&
    typeof surah.audioFilesize === "number" &&
    typeof surah.seoDescription === "string" &&
    Array.isArray(surah.seoKeywords) &&
    surah.seoKeywords.every((keyword: any) => typeof keyword === "string")
  )
}
