import * as React from "react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { ChevronLeft, Download } from "lucide-react"

import { Reader, Sura } from "@/types/data"
import { GetDictionary, Locale } from "@/types/i18n"
import { formatDuration, formatFileSize } from "@/lib/format"
import { getDictionary } from "@/lib/i18n"
import { getReaderById } from "@/lib/reciters"
import { getAllSurahsByReader } from "@/lib/surahs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { AudioPlayButton } from "@/components/audio-play-button"
import { PlayingIndicator } from "@/components/playing-indicator"

type Props = {
  params: Promise<{ locale: Locale; readerId: string }>
}

export default async function ReaderPage({ params }: Props) {
  const { locale, readerId } = await params
  const reader = getReaderById(locale, readerId)
  const surahs = getAllSurahsByReader(locale, readerId)
  const dict = (await getDictionary(locale)).reader

  if (!reader) notFound()

  return (
    <div className="mx-auto mt-10 flex w-full max-w-7xl flex-col items-center px-3">
      <div className="w-full *:w-full sm:*:w-auto">
        <Button asChild variant="secondary">
          <Link href={`/${locale}#${readerId}`}>
            {dict.backToReaders}{" "}
            <ChevronLeft className={locale !== "ar" ? "rotate-180" : ""} />
          </Link>
        </Button>
      </div>

      {/* Reader Card */}
      <Card className="dark:bg-card/50 mx-auto mt-4 w-full items-center justify-center py-20 text-center">
        <h1 className="text-primary text-3xl sm:text-4xl">{reader.name}</h1>
      </Card>

      {/* Surahs List */}
      <SurahsList reader={reader} surahs={surahs} locale={locale} dict={dict} />
    </div>
  )
}

function SurahsList({
  surahs,
  reader,
  locale,
  dict,
}: {
  surahs: Sura[]
  reader: Reader
  locale: Locale
  dict: GetDictionary["reader"]
}) {
  return (
    <div className="mt-20 w-full">
      <div className="flex w-full items-center justify-start gap-3">
        <p className="mb-0.5 text-nowrap">{dict.surahsList}</p>
        <Badge variant="outline" className="py-0.1 rounded-xs px-1.5">
          {reader.surahsCount}
        </Badge>
        <Separator className="grow data-[orientation=horizontal]:w-auto" />
      </div>
      <div className="mx-auto mt-8 flex w-full flex-col gap-6">
        {surahs.map((sura) => (
          <SuraCard
            key={sura.id}
            sura={sura}
            reader={reader}
            locale={locale}
            dict={dict.surahCard}
          />
        ))}
      </div>
    </div>
  )
}

function SuraCard({
  sura,
  reader,
  locale,
  dict,
}: {
  sura: Sura
  reader: Reader
  locale: Locale
  dict: GetDictionary["reader"]["surahCard"]
}) {
  const size = formatFileSize(sura.audioFilesize)
  const duration = formatDuration(sura.audioLength)

  return (
    <Card id={sura.id} className="scroll-mt-28 gap-y-0 overflow-clip p-0!">
      <div className="flex w-full flex-row items-center justify-between gap-x-2 px-4">
        <Link href={`/${locale}/${reader.id}/${sura.id}`} className="grow py-5">
          <p className="font-sans text-lg font-semibold text-nowrap">
            {sura.name}
          </p>
        </Link>
        <div className="flex items-center gap-2">
          <PlayingIndicator audioUrl={sura.audioUrl} />
          {/* Download File */}
          <a
            href={sura.audioUrl}
            download={`${sura.name}-${reader.name}.mp3`}
            rel="noopener noreferrer"
            target="_blank"
          >
            <Button variant="secondary" size="icon">
              <Download />
            </Button>
          </a>
          {/* Play Audio Button */}
          <AudioPlayButton sura={sura} reader={reader} />
        </div>
      </div>
      {/* File Details */}
      <div className="bg-muted flex w-full gap-4 px-4 py-0.5 **:text-xs">
        <p>
          <span className="text-muted-foreground">{dict.duration} :</span>{" "}
          {duration}
        </p>
        <p>
          <span className="text-muted-foreground">{dict.size} :</span>
          {size}
        </p>
        <p className="ms-auto">
          <span className="text-muted-foreground">{dict.order} :</span>{" "}
          {sura.id}
        </p>
      </div>
    </Card>
  )
}
