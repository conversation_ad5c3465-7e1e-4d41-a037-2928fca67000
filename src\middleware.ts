import { NextRequest, NextResponse } from "next/server"

import { getLocaleFromHeaders, pathnameIsMissingLocale } from "@/lib/i18n"

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // Redirect if there is no locale in the cookie
  if (pathnameIsMissingLocale(pathname)) {
    const local = getLocaleFromHeaders(request)
    return NextResponse.redirect(
      new URL(
        `/${local}${pathname.startsWith("/") ? "" : "/"}${pathname}`,
        request.url
      )
    )
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|icon.png|og-image-ar.png|og-image-en.png|robots.txt|sitemap.xml|manifest.json|reader-files).*)",
  ],
}
