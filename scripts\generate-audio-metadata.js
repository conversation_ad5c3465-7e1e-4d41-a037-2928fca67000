#!/usr/bin/env node

const path = require('path');
const { loadSurahMetadata, createSurahLookupMap, validateAudioFileCoverage } = require('./utils/surah-mapper');
const { processAudioFiles, generateProcessingSummary } = require('./utils/audio-processor');
const { scanReaderDirectories, scanAudioFiles, loadReaderMeta, writeJsonFile } = require('./utils/file-utils');
const { generateCompleteReaderData, generateDataFilePaths, validateGeneratedData } = require('./utils/data-generator');

/**
 * Parse command line arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    backup: false,
    verbose: false,
    reader: null
  };
  
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--backup':
        options.backup = true;
        break;
      case '--verbose':
        options.verbose = true;
        break;
      case '--reader':
        if (i + 1 < args.length) {
          options.reader = args[i + 1];
          i++; // Skip next argument
        }
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
    }
  }
  
  return options;
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
🎵 Audio Metadata Generator for Quran Reciters

Usage: node scripts/generate-audio-metadata.js [options]

Options:
  --dry-run         Preview changes without writing files
  --backup          Create backups before overwriting existing files
  --verbose         Show detailed logging information
  --reader <name>   Process only the specified reader
  --help, -h        Show this help message

Examples:
  node scripts/generate-audio-metadata.js
  node scripts/generate-audio-metadata.js --dry-run
  node scripts/generate-audio-metadata.js --reader husary --backup
  `);
}

/**
 * Process a single reader
 * @param {Object} readerInfo - Reader information object
 * @param {Object} surahMap - Surah lookup map
 * @param {Object} options - Command line options
 */
async function processReader(readerInfo, surahMap, options) {
  console.log(`\n🎙️  Processing reader: ${readerInfo.id}`);
  console.log(`   Path: ${readerInfo.path}`);
  
  try {
    // Load reader metadata
    const readerMeta = loadReaderMeta(readerInfo.metaPath);
    if (!readerMeta) {
      console.error(`❌ Cannot process ${readerInfo.id}: missing or invalid meta.json`);
      return false;
    }
    
    // Scan audio files
    const audioFiles = scanAudioFiles(readerInfo.surahsPath);
    if (audioFiles.length === 0) {
      console.warn(`⚠️  No audio files found for ${readerInfo.id}`);
      return false;
    }
    
    // Validate audio file coverage
    const validation = validateAudioFileCoverage(audioFiles, surahMap);
    
    // Process audio files to extract metadata
    console.log(`🎵 Extracting metadata from ${audioFiles.length} audio files...`);
    const processedFiles = await processAudioFiles(audioFiles, (current, total, filename) => {
      if (options.verbose) {
        console.log(`   [${current}/${total}] ${filename}`);
      }
    });
    
    // Generate processing summary
    const summary = generateProcessingSummary(processedFiles);
    console.log(`📊 Processing Summary:`);
    console.log(`   Total files: ${summary.totalFiles}`);
    console.log(`   Successful: ${summary.successful}`);
    console.log(`   Failed: ${summary.failed}`);
    console.log(`   Total duration: ${summary.totalDurationFormatted}`);
    console.log(`   Total size: ${summary.totalSizeFormatted}`);
    console.log(`   Average duration: ${summary.averageDurationFormatted}`);
    console.log(`   Average size: ${summary.averageSizeFormatted}`);
    
    // Generate complete data structure
    const generatedData = generateCompleteReaderData(readerInfo, processedFiles, surahMap, readerMeta);
    
    // Validate generated data
    const dataValidation = validateGeneratedData(generatedData);
    if (!dataValidation.isValid) {
      console.error(`❌ Data validation failed for ${readerInfo.id}:`);
      dataValidation.issues.forEach(issue => console.error(`   - ${issue}`));
      return false;
    }
    
    if (dataValidation.warnings.length > 0) {
      console.warn(`⚠️  Data validation warnings for ${readerInfo.id}:`);
      dataValidation.warnings.forEach(warning => console.warn(`   - ${warning}`));
    }
    
    // Write data files
    if (options.dryRun) {
      console.log(`🔍 DRY RUN: Would write data files for ${readerInfo.id}`);
      console.log(`   English: ${generatedData.en.surahs.length} surahs`);
      console.log(`   Arabic: ${generatedData.ar.surahs.length} surahs`);
    } else {
      await writeReaderData(readerInfo.id, generatedData, options);
    }
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error processing reader ${readerInfo.id}:`, error.message);
    if (options.verbose) {
      console.error(error.stack);
    }
    return false;
  }
}

/**
 * Write reader data to files
 * @param {string} readerId - Reader ID
 * @param {Object} data - Generated data object
 * @param {Object} options - Command line options
 */
async function writeReaderData(readerId, data, options) {
  console.log(`💾 Writing data files for ${readerId}...`);
  
  for (const locale of ['en', 'ar']) {
    const paths = generateDataFilePaths(readerId, locale);
    
    try {
      // Write reader metadata
      writeJsonFile(paths.readerMetaPath, data[locale].readerMeta, options.backup);
      
      // Write surahs data
      writeJsonFile(paths.surahsPath, data[locale].surahs, options.backup);
      
      console.log(`   ✅ ${locale.toUpperCase()}: ${data[locale].surahs.length} surahs written`);
      
    } catch (error) {
      console.error(`❌ Error writing ${locale} data for ${readerId}:`, error.message);
      throw error;
    }
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🎵 Audio Metadata Generator for Quran Reciters');
  console.log('================================================\n');
  
  try {
    // Parse command line arguments
    const options = parseArguments();
    
    if (options.verbose) {
      console.log('🔧 Options:', options);
    }
    
    // Load surah metadata
    console.log('📖 Loading surah metadata...');
    const surahsArray = await loadSurahMetadata();
    const surahMap = createSurahLookupMap(surahsArray);
    
    // Scan reader directories
    console.log('\n📁 Scanning reader directories...');
    const readers = scanReaderDirectories();
    
    if (readers.length === 0) {
      console.error('❌ No reader directories found');
      process.exit(1);
    }
    
    // Filter readers if specific reader requested
    let readersToProcess = readers;
    if (options.reader) {
      readersToProcess = readers.filter(r => r.id === options.reader);
      if (readersToProcess.length === 0) {
        console.error(`❌ Reader '${options.reader}' not found`);
        console.log('Available readers:', readers.map(r => r.id).join(', '));
        process.exit(1);
      }
    }
    
    console.log(`\n🎯 Processing ${readersToProcess.length} reader(s)...`);
    
    // Process each reader
    let successCount = 0;
    let failCount = 0;
    
    for (const reader of readersToProcess) {
      const success = await processReader(reader, surahMap, options);
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
    }
    
    // Final summary
    console.log('\n📋 Final Summary');
    console.log('================');
    console.log(`Total readers processed: ${readersToProcess.length}`);
    console.log(`Successful: ${successCount}`);
    console.log(`Failed: ${failCount}`);
    
    if (options.dryRun) {
      console.log('\n🔍 This was a dry run - no files were modified');
    }
    
    if (failCount > 0) {
      console.log('\n⚠️  Some readers failed to process. Check the logs above for details.');
      process.exit(1);
    } else {
      console.log('\n✅ All readers processed successfully!');
    }
    
  } catch (error) {
    console.error('\n❌ Fatal error:', error.message);
    if (options && options.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { main, processReader, parseArguments };
