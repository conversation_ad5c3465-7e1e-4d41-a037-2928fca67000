"use client"

import * as React from "react"

import { Reader, Sura } from "@/types/data"

export type Playing = {
  reader: Reader
  sura: Sura
}

type AudioPlayingContextValue = {
  playing: Playing | null
  isStopped: boolean
  setPlaying: React.Dispatch<React.SetStateAction<Playing | null>>
  setIsStopped: React.Dispatch<React.SetStateAction<boolean>>
  playButtonRef: React.RefObject<HTMLButtonElement>
}
const AudioPlayingContext =
  React.createContext<AudioPlayingContextValue | null>(null)

export function AudioPlayingProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const [playing, setPlaying] = React.useState<Playing | null>(null)
  const [isStopped, setIsStopped] = React.useState(true)
  const playButtonRef = React.useRef<HTMLButtonElement>(null!)

  return (
    <AudioPlayingContext
      value={{ playing, setPlaying, isStopped, setIsStopped, playButtonRef }}
    >
      {children}
    </AudioPlayingContext>
  )
}

export function useAudioPlaying() {
  const context = React.useContext(AudioPlayingContext)

  if (!context) {
    throw new Error(
      "useAudioPlaying must be used within a AudioPlayingProvider"
    )
  }

  return context
}
