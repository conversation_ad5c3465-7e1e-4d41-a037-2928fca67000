import fs from "fs"
import path from "path"
import * as React from "react"

import { <PERSON> } from "@/types/data"
import { Locale } from "@/types/i18n"

/**
 * returns an array of all Readers
 */
export const getReciters = React.cache((locale: Locale): Reader[] => {
  // Validate locale parameter
  if (!locale || (locale !== "ar" && locale !== "en")) {
    throw new Error(`Invalid locale: ${locale}. Must be 'ar' or 'en'`)
  }

  const readers: Reader[] = []
  const dataPath = path.join(process.cwd(), "src", "data", locale)
  const entries = fs.readdirSync(dataPath, { withFileTypes: true })

  // Check if the locale directory exists
  if (!fs.existsSync(dataPath)) {
    console.warn(`Data directory not found for locale: ${locale}`)
    return []
  }

  try {
    // Return all readers for the locale

    for (const entry of entries) {
      if (entry.isDirectory()) {
        const readerId = entry.name
        const reader = getReaderById(locale, readerId)

        if (reader) {
          readers.push(reader)
        }
      }
    }

    // Sort readers by name for consistent ordering
    return readers.sort((a, b) => a.name.localeCompare(b.name))
  } catch (error) {
    console.error(`Error retrieving reciters data for locale ${locale}:`, error)
    return []
  }
})

/**
 * Get a specific reader by ID
 */
export const getReaderById = React.cache(
  (locale: Locale, id: string): Reader | null => {
    const readerPath = path.join(
      process.cwd(),
      "src",
      "data",
      locale,
      id,
      "reader-meta.json"
    )

    if (!fs.existsSync(readerPath)) {
      return null
    }

    try {
      const readerData = fs.readFileSync(readerPath, "utf8")
      const reader: Reader = JSON.parse(readerData)

      // Validate the reader data structure
      if (!isValidReader(reader)) {
        console.warn(
          `Invalid reader data structure for ${id} in locale ${locale}`
        )
        return null
      }

      return reader
    } catch (error) {
      console.error(
        `Error reading reader data for ${id} in locale ${locale}:`,
        error
      )
      return null
    }
  }
)

/**
 * Validate reader data structure
 */
function isValidReader(reader: any): reader is Reader {
  return (
    reader &&
    typeof reader === "object" &&
    typeof reader.id === "string" &&
    typeof reader.name === "string" &&
    typeof reader.surahsCount === "number" &&
    typeof reader.seoDescription === "string" &&
    Array.isArray(reader.seoKeywords) &&
    reader.seoKeywords.every((keyword: any) => typeof keyword === "string")
  )
}
