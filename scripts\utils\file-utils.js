const fs = require('fs');
const path = require('path');

/**
 * Scan the reader-files directory for all reader subdirectories
 * @param {string} readerFilesPath - Path to the reader-files directory
 * @returns {Array} Array of reader directory information
 */
function scanReaderDirectories(readerFilesPath = null) {
  const basePath = readerFilesPath || path.join(process.cwd(), 'public', 'reader-files');
  
  console.log(`📁 Scanning reader directories in: ${basePath}`);
  
  if (!fs.existsSync(basePath)) {
    throw new Error(`Reader files directory not found: ${basePath}`);
  }
  
  const readers = [];
  const entries = fs.readdirSync(basePath, { withFileTypes: true });
  
  for (const entry of entries) {
    if (entry.isDirectory()) {
      const readerPath = path.join(basePath, entry.name);
      const surahsPath = path.join(readerPath, 'surahs');
      const metaPath = path.join(readerPath, 'meta.json');
      
      // Check if this is a valid reader directory
      const hasMetaFile = fs.existsSync(metaPath);
      const hasSurahsDir = fs.existsSync(surahsPath);
      
      if (hasMetaFile || hasSurahsDir) {
        readers.push({
          id: entry.name,
          path: readerPath,
          surahsPath: surahsPath,
          metaPath: metaPath,
          hasMetaFile: hasMetaFile,
          hasSurahsDir: hasSurahsDir
        });
      } else {
        console.warn(`⚠️  Skipping ${entry.name}: no meta.json or surahs directory found`);
      }
    }
  }
  
  console.log(`✅ Found ${readers.length} reader directories`);
  return readers;
}

/**
 * Scan audio files in a reader's surahs directory
 * @param {string} surahsPath - Path to the surahs directory
 * @returns {Array} Array of audio file paths
 */
function scanAudioFiles(surahsPath) {
  if (!fs.existsSync(surahsPath)) {
    console.warn(`⚠️  Surahs directory not found: ${surahsPath}`);
    return [];
  }
  
  const audioFiles = [];
  const entries = fs.readdirSync(surahsPath);
  
  for (const entry of entries) {
    const filePath = path.join(surahsPath, entry);
    const stat = fs.statSync(filePath);
    
    if (stat.isFile() && entry.toLowerCase().endsWith('.mp3')) {
      audioFiles.push(filePath);
    }
  }
  
  // Sort files by name for consistent ordering
  audioFiles.sort();
  
  console.log(`   Found ${audioFiles.length} audio files in ${path.basename(surahsPath)}`);
  return audioFiles;
}

/**
 * Load reader metadata from meta.json file
 * @param {string} metaPath - Path to the meta.json file
 * @returns {Object|null} Reader metadata or null if not found
 */
function loadReaderMeta(metaPath) {
  if (!fs.existsSync(metaPath)) {
    console.warn(`⚠️  Reader meta.json not found: ${metaPath}`);
    return null;
  }
  
  try {
    const rawData = fs.readFileSync(metaPath, 'utf8');
    const meta = JSON.parse(rawData);
    
    // Validate required fields
    if (!meta.name_en || !meta.name_ar) {
      console.warn(`⚠️  Invalid meta.json structure in ${metaPath}: missing name_en or name_ar`);
      return null;
    }
    
    return meta;
  } catch (error) {
    console.error(`❌ Error reading meta.json from ${metaPath}:`, error.message);
    return null;
  }
}

/**
 * Ensure directory exists, create if it doesn't
 * @param {string} dirPath - Directory path to ensure
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dirPath}`);
  }
}

/**
 * Write JSON data to file with proper formatting
 * @param {string} filePath - Path to write the file
 * @param {Object} data - Data to write as JSON
 * @param {boolean} backup - Whether to create a backup if file exists
 */
function writeJsonFile(filePath, data, backup = false) {
  try {
    // Create backup if requested and file exists
    if (backup && fs.existsSync(filePath)) {
      const backupPath = `${filePath}.backup.${Date.now()}`;
      fs.copyFileSync(filePath, backupPath);
      console.log(`💾 Created backup: ${path.basename(backupPath)}`);
    }
    
    // Ensure directory exists
    const dir = path.dirname(filePath);
    ensureDirectoryExists(dir);
    
    // Write JSON file with proper formatting
    const jsonString = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonString, 'utf8');
    
    console.log(`✅ Written: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error writing file ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * Read JSON file safely
 * @param {string} filePath - Path to the JSON file
 * @returns {Object|null} Parsed JSON data or null if error
 */
function readJsonFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const rawData = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(rawData);
  } catch (error) {
    console.error(`❌ Error reading JSON file ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Get file information (size, modification time, etc.)
 * @param {string} filePath - Path to the file
 * @returns {Object|null} File information or null if error
 */
function getFileInfo(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const stats = fs.statSync(filePath);
    return {
      size: stats.size,
      sizeKB: Math.round(stats.size / 1024),
      modified: stats.mtime,
      created: stats.birthtime,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory()
    };
  } catch (error) {
    console.error(`❌ Error getting file info for ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Clean up old backup files (older than specified days)
 * @param {string} directory - Directory to clean
 * @param {number} daysOld - Files older than this many days will be deleted
 */
function cleanupBackups(directory, daysOld = 7) {
  try {
    if (!fs.existsSync(directory)) {
      return;
    }
    
    const entries = fs.readdirSync(directory);
    const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    let deletedCount = 0;
    
    for (const entry of entries) {
      if (entry.includes('.backup.')) {
        const filePath = path.join(directory, entry);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }
    }
    
    if (deletedCount > 0) {
      console.log(`🧹 Cleaned up ${deletedCount} old backup files`);
    }
  } catch (error) {
    console.error(`❌ Error cleaning up backups:`, error.message);
  }
}

/**
 * Validate file path and extension
 * @param {string} filePath - File path to validate
 * @param {Array} allowedExtensions - Array of allowed extensions (e.g., ['.mp3', '.wav'])
 * @returns {boolean} True if valid
 */
function validateFilePath(filePath, allowedExtensions = ['.mp3']) {
  if (!filePath || typeof filePath !== 'string') {
    return false;
  }
  
  const ext = path.extname(filePath).toLowerCase();
  return allowedExtensions.includes(ext);
}

module.exports = {
  scanReaderDirectories,
  scanAudioFiles,
  loadReaderMeta,
  ensureDirectoryExists,
  writeJsonFile,
  readJsonFile,
  getFileInfo,
  cleanupBackups,
  validateFilePath
};
