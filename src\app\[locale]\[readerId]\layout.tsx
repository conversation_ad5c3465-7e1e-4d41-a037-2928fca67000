import { Metadata, ResolvingMetadata } from "next"
import { notFound } from "next/navigation"

import { Locale } from "@/types/i18n"
import { getDictionary } from "@/lib/i18n"
import { getReaderById, getReciters } from "@/lib/reciters"

type Props = {
  params: Promise<{ locale: Locale; readerId: string }>
}

export async function generateStaticParams() {
  const readers = getReciters("ar")
  return readers.map((reader) => ({
    readerId: reader.id,
  }))
}

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { locale, readerId } = await params
  const reader = getReaderById(locale, readerId)
  const dictionary = (await getDictionary(locale)).metadata
  const dict = dictionary.readerPage

  const previousMetadata = await parent
  const previousOpenGraph = previousMetadata.openGraph || {}
  const previousTwitter = previousMetadata.twitter || {}

  if (!reader) return notFound()

  return {
    title: `${dict.title} ${reader.name}`,
    description: dict.description.replace("{readerName}", reader.name),
    keywords: reader.seoKeywords,
    openGraph: {
      ...previousOpenGraph,
      title: `${dict.title} ${reader.name}`,
      description: dict.description.replace("{readerName}", reader.name),
    },
    twitter: {
      ...previousTwitter,
      title: `${dict.title} ${reader.name}`,
      description: dict.description.replace("{readerName}", reader.name),
    },
  }
}

export default function ReaderLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}
