const fs = require('fs');
const path = require('path');
const { parseFile } = require('music-metadata');

/**
 * Extract metadata from an audio file
 * @param {string} filePath - Path to the audio file
 * @returns {Promise<Object>} Audio metadata object
 */
async function extractAudioMetadata(filePath) {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Audio file not found: ${filePath}`);
    }
    
    // Get file stats for size
    const stats = fs.statSync(filePath);
    const fileSizeBytes = stats.size;
    const fileSizeKB = Math.round(fileSizeBytes / 1024);
    
    // Parse audio metadata
    const metadata = await parseFile(filePath);
    
    // Extract duration (in seconds)
    const duration = metadata.format.duration || 0;
    const durationSeconds = Math.round(duration);
    
    // Extract additional metadata
    const format = metadata.format.container || 'unknown';
    const bitrate = metadata.format.bitrate || 0;
    const sampleRate = metadata.format.sampleRate || 0;
    
    const result = {
      duration: durationSeconds,
      size: fileSizeBytes,
      sizeKB: fileSizeKB,
      format: format,
      bitrate: bitrate,
      sampleRate: sampleRate,
      filePath: filePath
    };
    
    // Validate extracted data
    if (durationSeconds <= 0) {
      console.warn(`⚠️  Warning: ${path.basename(filePath)} has invalid duration: ${durationSeconds}s`);
    }
    
    if (fileSizeKB <= 0) {
      console.warn(`⚠️  Warning: ${path.basename(filePath)} has invalid file size: ${fileSizeKB}KB`);
    }
    
    return result;
    
  } catch (error) {
    console.error(`❌ Error processing audio file ${filePath}:`, error.message);
    throw error;
  }
}

/**
 * Process multiple audio files and extract metadata
 * @param {Array} audioFiles - Array of audio file paths
 * @param {Function} progressCallback - Optional callback for progress updates
 * @returns {Promise<Array>} Array of metadata objects
 */
async function processAudioFiles(audioFiles, progressCallback = null) {
  const results = [];
  const total = audioFiles.length;
  
  console.log(`🎵 Processing ${total} audio files...`);
  
  for (let i = 0; i < audioFiles.length; i++) {
    const audioFile = audioFiles[i];
    const filename = path.basename(audioFile);
    
    try {
      if (progressCallback) {
        progressCallback(i + 1, total, filename);
      } else {
        console.log(`   Processing ${i + 1}/${total}: ${filename}`);
      }
      
      const metadata = await extractAudioMetadata(audioFile);
      results.push({
        file: audioFile,
        filename: filename,
        metadata: metadata
      });
      
    } catch (error) {
      console.error(`❌ Failed to process ${filename}:`, error.message);
      // Continue processing other files
      results.push({
        file: audioFile,
        filename: filename,
        metadata: null,
        error: error.message
      });
    }
  }
  
  const successful = results.filter(r => r.metadata !== null).length;
  const failed = results.length - successful;
  
  console.log(`✅ Audio processing complete: ${successful} successful, ${failed} failed`);
  
  return results;
}

/**
 * Validate audio metadata for reasonable values
 * @param {Object} metadata - Audio metadata object
 * @returns {Object} Validation results
 */
function validateAudioMetadata(metadata) {
  const issues = [];
  const warnings = [];
  
  // Check duration
  if (metadata.duration <= 0) {
    issues.push('Invalid duration (must be > 0 seconds)');
  } else if (metadata.duration < 10) {
    warnings.push('Very short duration (< 10 seconds)');
  } else if (metadata.duration > 7200) { // 2 hours
    warnings.push('Very long duration (> 2 hours)');
  }
  
  // Check file size
  if (metadata.sizeKB <= 0) {
    issues.push('Invalid file size (must be > 0 KB)');
  } else if (metadata.sizeKB < 100) {
    warnings.push('Very small file size (< 100 KB)');
  } else if (metadata.sizeKB > 50000) { // 50 MB
    warnings.push('Very large file size (> 50 MB)');
  }
  
  // Check bitrate
  if (metadata.bitrate > 0) {
    if (metadata.bitrate < 64) {
      warnings.push('Low bitrate (< 64 kbps)');
    } else if (metadata.bitrate > 320) {
      warnings.push('High bitrate (> 320 kbps)');
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues: issues,
    warnings: warnings
  };
}

/**
 * Generate a summary of processed audio files
 * @param {Array} processedFiles - Array of processed file objects
 * @returns {Object} Summary statistics
 */
function generateProcessingSummary(processedFiles) {
  const successful = processedFiles.filter(f => f.metadata !== null);
  const failed = processedFiles.filter(f => f.metadata === null);
  
  if (successful.length === 0) {
    return {
      totalFiles: processedFiles.length,
      successful: 0,
      failed: failed.length,
      totalDuration: 0,
      totalSize: 0,
      averageDuration: 0,
      averageSize: 0
    };
  }
  
  const totalDuration = successful.reduce((sum, f) => sum + f.metadata.duration, 0);
  const totalSize = successful.reduce((sum, f) => sum + f.metadata.sizeKB, 0);
  
  return {
    totalFiles: processedFiles.length,
    successful: successful.length,
    failed: failed.length,
    totalDuration: totalDuration,
    totalDurationFormatted: formatDuration(totalDuration),
    totalSize: totalSize,
    totalSizeFormatted: formatFileSize(totalSize * 1024),
    averageDuration: Math.round(totalDuration / successful.length),
    averageDurationFormatted: formatDuration(Math.round(totalDuration / successful.length)),
    averageSize: Math.round(totalSize / successful.length),
    averageSizeFormatted: formatFileSize((totalSize / successful.length) * 1024)
  };
}

/**
 * Format duration in seconds to human-readable format
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration
 */
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

/**
 * Format file size in bytes to human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
function formatFileSize(bytes) {
  if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  } else if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  } else {
    return `${bytes} bytes`;
  }
}

module.exports = {
  extractAudioMetadata,
  processAudioFiles,
  validateAudioMetadata,
  generateProcessingSummary,
  formatDuration,
  formatFileSize
};
